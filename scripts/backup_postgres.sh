#!/bin/bash

# ALWAYS use an absolute path for your backup directory
BACKUP_DIR="$HOME/pg_backups"

DB_USER="postgres" # The user that requires a password
DB_NAME="line-art-generator"
DB_PASSWORD="the_password_for_myuser" # This is what the script provides

PYTHON_UPLOAD_SCRIPT="$HOME/line-art-generator-v2/scripts/google_drive/upload_backup.py"

# Ensure the backup directory exists
mkdir -p "$BACKUP_DIR"

# Create a unique, timestamped filename with its full path
FILENAME="backup-$(date +%Y-%m-%d_%H-%M-%S).sql.gz"
BACKUP_FILE_PATH="$BACKUP_DIR/$FILENAME"

# Step 1: Authenticate with Docker (happens automatically because your user is in the 'docker' group)
# Step 2: Authenticate pg_dump with the PostgreSQL server (happens via PGPASSWORD)
docker exec \
  $(docker ps --filter "ancestor=postgres" --format "{{.Names}}") \
  pg_dump -U "$DB_USER" -d "$DB_NAME"| gzip > "$BACKUP_FILE_PATH"

# Check if the backup and compression succeeded
if [ $? -eq 0 ]; then
  echo "Backup successful."
  echo "Calling Python upload script..."
  
  # --- THE KEY STEP ---
  # Execute the Python script and pass the full file path as an argument
  uv run "$PYTHON_UPLOAD_SCRIPT" "$BACKUP_FILE_PATH"
else
  echo "Backup failed. Upload script will not be called."
  # Remove the failed (and likely empty) backup file
  rm "$BACKUP_FILE_PATH"
  exit 1
fi

echo "Process finished."
